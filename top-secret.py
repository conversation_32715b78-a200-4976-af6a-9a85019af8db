import time
import os
import shutil
import random
from patchright.sync_api import sync_playwright
from python_ghost_cursor.playwright_sync import create_cursor
from datetime import datetime
#from random import randrange, uniform

from oxymouse import OxyMouse
import logging
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

#BING_SELECTOR = "#b_results .b_wpt_bl h2 a, #b_results > li > h2 a"
#BING_SELECTOR = "h2a[h*='SERP']"
BING_SELECTOR = """
#b_results .b_algo h2 a[h],
#b_results .b_ans h2 a[h], 
#b_results .b_wpt_bl h2 a[h],
#b_results h2 a[h],
.news-card h2 a[h],
h2a[h*='SERP'] 
 """
BING_NEXT="a.sb_pagN[title='Next page']"

CHROMIUM_ARGS= [
		'--no-sandbox',
		'--disable-setuid-sandbox',
		'--no-first-run',
		'--disable-blink-features=AutomationControlled',
		'--start-maximized'
	  ]
def get_viewport_size(page):
    return page.evaluate("""() => {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        }
    }""")

def get_domain(url):
    """Extract domain from URL, ignoring subpaths"""
    try:
        parsed = urlparse(url)
        # If no scheme provided, add one to help urlparse
        if not parsed.netloc:
            parsed = urlparse('https://' + url)
        return parsed.netloc.lower().replace('www.', '')
    except Exception as e:
        logger.error(f"Error parsing URL {url}: {str(e)}")
        return url.lower().replace('www.', '')
    
def generate_random_movements() -> list[tuple[int, int]]:
    mouse = OxyMouse(algorithm="gaussian")
    movements = mouse.generate_coordinates()
    return movements

def move_mouse_smoothly(page, movements: list[tuple[int, int]]):
    logger.info(f"In move_mouse_smoothly")
    for x, y in movements:
        page.mouse.move(x, y)
        time.sleep(random.uniform(0.001, 0.003))  # Add small random delays

def type_like_human(page, element, text):
    for char in text:
        page.keyboard.type(char, delay=random.randint(25, 75))  # Simulate typing speed
        
def old_human_like_click(page, cursor, target_element):
    logger.info(f"In human_like_click: target_element: {target_element}")
    movements = generate_random_movements()
    move_mouse_smoothly(page, movements)
    cursor.click(target_element)
    logger.info(f"clicked {target_element}")
    
def human_like_click(page, cursor, target_element):
    logger.info(f"In human_like_click: target_element: {target_element}")
    try:
        # Convert Locator to ElementHandle if necessary
        if hasattr(target_element, 'element_handle'):
            target_element = target_element.element_handle()
        elif hasattr(target_element, 'first'):
            target_element = target_element.first.element_handle()
            
        viewport = get_viewport_size(page)
        start_x = random.randint(0, viewport['width'])
        start_y = random.randint(0, viewport['height'])
        # Get element's bounding box
        box = target_element.bounding_box()
        if not box:
            logger.error("Could not get element bounding box")
            return False

        # Create OxyMouse instance with gaussian algorithm
        mouse = OxyMouse(algorithm="gaussian")
        
        # Generate path to element (aim for center of element)
        target_x = int(box['x'] + box['width'] / 2)
        target_y = int(box['y'] + box['height'] / 2)
        
        # Generate the movement coordinates
        movements = mouse.generate_coordinates(
            from_x=start_x, 
            from_y=start_y,
            to_x=target_x,
            to_y=target_y
        )

        # Execute the movements
        for x, y in movements:
            page.mouse.move(x, y)
            time.sleep(random.uniform(0.001, 0.003))
        # Add a small random delay before clicking
        #time.sleep(random.uniform(0.2, 0.5))
        # Check if link will open in new tab
        target_blank = target_element.get_attribute('target') == '_blank'
        
        ''' if target_blank:
        # Use cursor.click() with a wait_for_new_page context
            with page.expect_navigation(wait_until="load", timeout=10000):
            # Let ghost-cursor handle the movement and click
            # Adding a small padding percentage to avoid clicking exactly on edges
                cursor.click(target_element, padding_percentage=0.2)
            new_page = new_page_info.value
            new_page.wait_for_load_state('load')
        logger.info(f"Successfully clicked element") '''
        if target_blank:
        # Wait for new page to be created
            with page.context.expect_page() as new_page_info:
                cursor.click(target_element, padding_percentage=0.2)
            new_page = new_page_info.value
            new_page.wait_for_load_state('load')
            logger.info(f"Successfully clicked element")
        return True

    except Exception as e:
        logger.error(f"Error in human_like_click: {str(e)}")
        return False
    
def human_like_scroll_with_mouse(page, cursor, max_scroll_height=None, scroll_amount=100):
    if max_scroll_height is None:
        max_scroll_height = page.evaluate('() => document.body.scrollHeight')
    current_scroll_height = 0
    while current_scroll_height < max_scroll_height:
        scroll_by = random.randint(scroll_amount // 2, scroll_amount)
        page.evaluate(f'window.scrollBy(0, {scroll_by})')
        current_scroll_height += scroll_by
        time.sleep(random.uniform(0.1, 0.5))
        
def find_element_or_scroll_down(page, cursor,target_url, max_attempts=10):
    """
    Waits for the target element or scrolls down to the bottom and clicks "Next page" if not found within attempts.
    """
    position_counter = 0
    logger.info(f"waiting for h2 BING-SELECTOR")
    page.wait_for_selector(BING_SELECTOR) 
    logger.info(f"found one of the selectors")
    target_domain = get_domain(target_url)  # NEW LINE
    logger.info(f"Looking for domain: {target_domain}")  # NEW LINE
    for attempt in range(max_attempts):
        logger.info(f"attempt: {attempt}")
        links = page.query_selector_all(BING_SELECTOR)
        # Get all links and their texts
        hrefs = [link.get_attribute("href") for link in links]
        logger.info(f"hrefs: {hrefs}")
        link_texts = [link.inner_text() for link in links]
        

        logger.info(f"Found {len(link_texts)} links on page {attempt + 1}")
        
        for idx, (link_text, href) in enumerate(zip(link_texts, hrefs)):
                # If href is None or empty, skip
            if not href:
                continue
            current_domain = get_domain(href)
                # Skip bing.com links
            if 'bing.com' in href.lower():
                continue
            #current_domain = get_domain(href)
            logger.info(f"Found link {idx + 1}: Text='{link_text}', URL={href}")
            position_counter += 1
            logger.info(f"link_text: {link_text}")
            if target_domain in current_domain:
                logger.info(f"Found {target_url} at position {position_counter}")
                target_link = links[idx]
                if human_like_click(page, cursor, target_link):
                    return position_counter
                else:
                    logger.info(f"Failed to click target link")
                    return -1   

    
        human_like_scroll_with_mouse(page, cursor)
        next_page_button = page.locator("a.sb_pagN[title='Next page']").first
        if next_page_button:
            human_like_click(page, cursor, next_page_button)
            logger.info(f"clicked next page button")
        else:
            logger.info(f"Next page button not found after {attempt + 1} attempts.")
            return -1
    return -1     
def random_scroll_and_click(page, cursor, min_scroll_time=2, max_scroll_time=8):
    """
    Randomly scroll on a page for a few seconds and click a random link.
    
    Args:
        page: Playwright page object
        cursor: Ghost cursor object
        min_scroll_time: Minimum time to scroll (seconds)
        max_scroll_time: Maximum time to scroll (seconds)
    """
    # First, scroll randomly for a while
    scroll_duration = random.uniform(min_scroll_time, max_scroll_time)
    start_time = time.time()
    
    while time.time() - start_time < scroll_duration:
        human_like_scroll_with_mouse(page, cursor, scroll_amount=random.randrange(100, 300))
    
    # Find all clickable links (excluding navigation elements, social media, etc.)
    links = page.locator("a:not([href^='#']):not([href^='javascript']):not([href*='facebook']):not([href*='twitter']):not([href*='linkedin'])")
    #logger.info(f"links: {links}")
    try:
        # Get count of links and select a random one
        link_count = links.count()
        if link_count == 0:
            logger.warning("No suitable links found on page")
            return False
            
        random_index = random.randrange(0, link_count)
        random_link = links.nth(random_index)
        
        # Log the link details
        link_url = random_link.get_attribute('href')
        link_text = random_link.inner_text()
        logger.info(f"Clicking random link #{random_index}: URL={link_url}, Text={link_text}")
        
        # Make sure link is in viewport
        random_link.scroll_into_view_if_needed()
        # Click the link with human-like movement
        human_like_click(page, cursor, random_link)
        return True
        
    except Exception as e:
        logger.error(f"Error during random click: {str(e)}")
        return False
      
def search_bing(search_term: str, target_url: str, debug: bool = False):
    # Add type validation at the start of the function
    if not isinstance(search_term, str):
        search_term = str(search_term)
    if not isinstance(target_url, str):
        target_url = str(target_url)
    logger.info(f"Starting search with term: {search_term}, target: {target_url}")
    user_data_dir = "/tmp/chrome_profile"
    begin=time.time()
    ctr=False
    # Ensure directory exists and has correct permissions
    try:
        os.makedirs(user_data_dir, mode=0o755, exist_ok=True)
    except Exception as e:
        logger.error(f"Failed to create user data directory: {e}")
        user_data_dir = "/tmp"  # Fallback to /tmp if creation fails
    logger.info(f"Debug mode is: {debug}")
    logger.info(f"Looking for URL: {target_url}")
    
    # Get target domain
    target_domain = get_domain(target_url)
    logger.info(f"Target domain is: {target_domain}")
    
    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            channel="chrome",
            headless=False,
            no_viewport=True,
            slow_mo=random.randrange(50, 100),
            args=CHROMIUM_ARGS,  # Add this to help with permissions
            ignore_default_args=["--enable-automation"]
            )# Or p.firefox.launch() 
        
        all_pages = browser.pages
        page = all_pages[0]
        page.goto('https://www.bing.com', timeout=0)
        interval=random.randrange(5)
        cursor = create_cursor(page)
        
        
        time.sleep(interval)
        search_box = page.wait_for_selector('textarea[name="q"]')
        human_like_click(page, cursor, search_box)  # Click on search box
        logger.info(f"clicked search box")
        type_like_human(page, search_box, search_term)
        page.keyboard.press('Enter')
        logger.info(f"pressed enter")
        #############
        #results page shows up
        page.wait_for_selector('#b_results')
        cursor = create_cursor(page)
        logger.info(f"created results page")
        logger.info(f"browser tabs: {len(browser.pages)}")
        position = find_element_or_scroll_down(page, cursor, target_url)
        ##we should now be on the target url page
        
        start_time = time.time()
        logger.info(f"start_time: {start_time}")
        logger.info(f"browser tabs: {len(browser.pages)}")
        for i, page in enumerate(browser.pages):
            logger.info(f"Tab {i + 1}: {page.url}")
        while len(browser.pages) < 2:
            if time.time() - start_time > 25:
                raise TimeoutError("New page didn't open within 25 seconds")
            time.sleep(0.1)
        logger.info(f"new page opened")
        # Close the original Bing page
        all_pages[0].close()
        logger.info(f"closed original bing page")
        # Get the new page (it will be the only one left)
        result_page = browser.pages[0]
        result_cursor = create_cursor(result_page)
        ctr=True
        logger.info(f"randomly clicking on page")
        random_scroll_and_click(result_page, result_cursor)
        

        browser.close()
        return position, time.time() - begin, ctr

